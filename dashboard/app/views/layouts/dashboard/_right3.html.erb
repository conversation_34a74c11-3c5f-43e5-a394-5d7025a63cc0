<%
  # 获取终端链接
  terminal_link = ''
  if defined?(@pinned_apps) && @pinned_apps
    @pinned_apps.each do |app|
      link = app.links.first
      if link && link.icon_uri.to_s == 'fas://terminal'
        terminal_link = link.url.to_s
        break
      end
    end
  end
%>

<div class="right3" style="margin-bottom: 30px;">
  <div style="color:#666666;">常用工具</div>
  <div style="width: 100%;height: 72px;border-radius: 16px;background: #5B93FF0D;display:flex;justify-content: space-around;">
    <div style="display: flex;align-items:center;gap:8px;cursor:pointer;" id="new-script-btn">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>
        <div>脚本任务</div>
        <div style="font-size: 12px;color:#666666;">运行.sh文件</div>
      </div>
    </div>
    <div style="display: flex;align-items:center;gap:8px;cursor:pointer;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>
        <div>桌面环境</div>
        <div style="font-size: 12px;color:#666666;">启动CentOS</div>
      </div>
    </div>
    <div style="display: flex;align-items:center;gap:8px;cursor:pointer;" onclick="redirectToFiles()">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>
        <div>文件管理</div>
        <div style="font-size: 12px;color:#666666;">查看文件目录</div>
      </div>
    </div>
    <div id="terminal-btn" style="display: flex;align-items:center;gap:8px;cursor:pointer;" onclick="showTerminalModal()">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>
        <div>启动终端</div>
        <div style="font-size: 12px;color:#666666;">shell命令</div>
      </div>
    </div>
  </div>
  <div style="margin-top: 24px;color:#666666;">常用任务</div>
  <div style="display: flex;gap: 90px;margin-top: 8px;padding-left:25px">
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
  </div>
  <div style="margin-top: 24px;color:#666666;">脚本任务</div>
  <div style="display: flex;gap: 90px;margin-top: 8px;padding-left:25px">
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>脚本训练</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>脚本训练2</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>AI训练</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
  </div>
  <div style="margin-top: 24px;color:#666666;">实例任务</div>
  <div style="display: flex;column-gap: 90px;row-gap: 24px;margin-top: 8px;padding-left:20px;flex-wrap: wrap;">
    <% @pinned_apps.each do |app| %>
      <%- link = app.links.first -%>
      <% next if link.icon_uri.to_s.match?(/:\/\//) %>
      <div style="text-align: center; cursor: pointer;" onclick="showAppModal('<%= link.url.to_s %>', '<%= link.title %>')">
        <div><img src="<%= link.icon_uri %>" style="width: 38px;"></div>
        <div><%= content_tag(:p, link.title) %></div>
      </div>
    <% end %>
  </div>

  <!-- 终端弹窗 -->
  <div id="terminal-modal" style="display:none; position: fixed; z-index: 9999; left: 0; top: 0; width: 100vw; height: 100vh; background: rgba(0,0,0,0.3);">
    <div style="background: #fff; border-radius: 16px; width: 90%; max-width: 1200px; height: 80%; margin: 5% auto; position: relative; box-shadow: 0 2px 16px rgba(0,0,0,0.15); display: flex; flex-direction: column;">
      <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px; border-bottom: 1px solid #eee;">
        <div style="font-size: 20px; font-weight: 600;">终端</div>
        <button type="button" id="close-terminal-modal" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">&times;</button>
      </div>
      <div style="flex: 1; padding: 0;">
        <iframe id="terminal-iframe" src="" frameborder="0" style="width: 100%; height: 100%; border: none;"></iframe>
      </div>
    </div>
  </div>

  <!-- App弹窗 -->
  <div id="app-modal" style="display:none; position: fixed; z-index: 9999; left: 0; top: 0; width: 100vw; height: 100vh; background: rgba(0,0,0,0.3);">
    <div style="background: #fff; border-radius: 16px; width: 90%; max-width: 1200px; height: 80%; margin: 5% auto; position: relative; box-shadow: 0 2px 16px rgba(0,0,0,0.15); display: flex; flex-direction: column;">
      <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px; border-bottom: 1px solid #eee;">
        <div id="app-modal-title" style="font-size: 20px; font-weight: 600;">应用</div>
        <button type="button" id="close-app-modal" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">&times;</button>
      </div>
      <div style="flex: 1; padding: 0;">
        <iframe id="app-iframe" src="" frameborder="0" style="width: 100%; height: 100%; border: none;"></iframe>
      </div>
    </div>
  </div>

  <!-- 新建任务 -->
  <div id="new-script-modal" style="display:none; position: fixed; z-index: 9999; left: 0; top: 0; width: 100vw; height: 100vh; background: rgba(0,0,0,0.3);">
    <div style="background: #fff; border-radius: 16px; width: 560px; margin: 100px auto; padding: 40px; position: relative; box-shadow: 0 2px 16px rgba(0,0,0,0.15);">
      <div style="font-size: 20px; font-weight: 600; margin-bottom: 18px;text-align:center;">新建脚本任务</div>
      <div id="new-script-task-form" autocomplete="off">
        <div style="margin-bottom: 14px;">
          <label for="task-name" style="display:block; margin-bottom: 4px;">任务名称</label>
          <input id="task-name" name="name" type="text" class="form-control input-placeholder-light" required>
        </div>
        <div style="margin-bottom: 14px;">
          <div class="form-group"><label for="script-path-on">脚本路径</label><input data-filepicker="true" data-show-hidden="true" data-target-file-type="dirs" data-file_picker_favorites="[{&quot;title1&quot;: &quot;项目目录&quot;, &quot;href&quot;: &quot;/home/<USER>" data-default-directory="/home/<USER>" readonly="readonly" value="/home/" class="form-control" type="text" name="script-path-on" id="script-path-on"><div><div id="modal-for-script-path-on" tabindex="-1" role="dialog" class="modal"><div class="modal-background"></div> <div role="document" class="modal-dialog modal-lg"><div class="modal-content"><div class="modal-header"> <button type="button" data-dismiss="modal" aria-label="退出" class="close"><span aria-hidden="true">×</span></button></div> <div class="modal-body"><div class="container-fluid"><div class="row"><div class="col-sm-5"><div class="panel panel-default"><div class="panel-heading">Favorites</div> <div class="list-group text-nowrap"><a role="button" class="list-group-item list-group-item-action"><span class="fa fa-folder">&nbsp;</span></a></div></div></div> <div class="col-sm-7"><nav aria-label="breadcrumb"><ol class="breadcrumb"><li aria-current="page" class="breadcrumb-item"><a role="button"></a></li><li aria-current="page" class="breadcrumb-item active"><a role="button"></a></li></ol></nav> <div class="form-group row"><label for="filter-for-script-path-on" class="col-sm-2 col-form-label"></label> <div class="col-sm-11"><input type="text" id="filter-for-script-path-on" placeholder="file.txt" class="form-control"></div></div> <div role="status"><span class="glyphicon glyphicon-refresh glyphicon-refresh-animate spinning">Loading...</span></div></div></div></div></div> <div class="modal-footer"><div type="button" data-dismiss="modal" class="btn btn-primary">选择</div> <div type="button" data-dismiss="modal" class="btn btn-secondary">退出</div></div></div></div></div></div></div>
        </div>
        <div style="margin-bottom: 18px;">
          <label for="script-name" style="display:block; margin-bottom: 4px;">脚本名称</label>
          <input id="script-name" name="script_name" type="text" class="form-control input-placeholder-light">
        </div>
        <div style="display: flex; justify-content: flex-end; gap: 12px;">
          <button type="button" class="btn btn-primary btn-block submit-button" id="task-add-queue">加入队列</button>
        </div>
      </div>
      <span id="close-script-modal" style="position: absolute; right: 16px; top: 12px; font-size: 22px; color: #888; cursor: pointer;">&times;</span>
    </div>
  </div>

  <% @app = BatchConnect::App.from_token "dev/ansys" %>
  <% @app.custom_javascript_files.each do |jsfile| %>
    <%= javascript_tag "(function(){\n" + jsfile.read + "\n}());" %>
  <% end %>

  <script>
    // 显示App弹窗
    function showAppModal(url, title) {
      const modal = document.getElementById('app-modal');
      const iframe = document.getElementById('app-iframe');
      const titleElement = document.getElementById('app-modal-title');

      if (url && url.trim() !== '') {
        iframe.src = url;
        titleElement.textContent = title || '应用';
        modal.style.display = 'block';
      } else {
        alert('应用链接不可用');
      }
    }

    // 显示终端弹窗
    function showTerminalModal() {
      const modal = document.getElementById('terminal-modal');
      const iframe = document.getElementById('terminal-iframe');
      const terminalUrl = "<%= terminal_link %>";

      if (terminalUrl && terminalUrl.trim() !== '') {
        iframe.src = terminalUrl;
        modal.style.display = 'block';
      } else {
        alert('终端服务不可用，请检查配置');
      }
    }

    // 关闭弹窗相关事件
    document.addEventListener('DOMContentLoaded', function() {
      // 关闭App弹窗
      document.getElementById('close-app-modal').onclick = function() {
        const modal = document.getElementById('app-modal');
        const iframe = document.getElementById('app-iframe');
        modal.style.display = 'none';
        iframe.src = ''; // 清空iframe源
      };

      // 点击App弹窗背景关闭
      document.getElementById('app-modal').onclick = function(e) {
        if (e.target === this) {
          const iframe = document.getElementById('app-iframe');
          this.style.display = 'none';
          iframe.src = ''; // 清空iframe源
        }
      };

      // 关闭终端弹窗
      document.getElementById('close-terminal-modal').onclick = function() {
        const modal = document.getElementById('terminal-modal');
        const iframe = document.getElementById('terminal-iframe');
        modal.style.display = 'none';
        iframe.src = ''; // 清空iframe源以停止终端连接
      };

      // 点击终端弹窗背景关闭
      document.getElementById('terminal-modal').onclick = function(e) {
        if (e.target === this) {
          const iframe = document.getElementById('terminal-iframe');
          this.style.display = 'none';
          iframe.src = ''; // 清空iframe源以停止终端连接
        }
      };
    });

    // 运行脚本文件弹窗相关
    document.getElementById('new-script-btn').onclick = function() {
      document.getElementById('new-script-modal').style.display = 'block';
    };
    document.getElementById('close-script-modal').onclick = function() {
      document.getElementById('new-script-modal').style.display = 'none';
    };
    document.getElementById('new-script-modal').onclick = function(e) {
      if (e.target === this) this.style.display = 'none';
    };

    // 加入队列按钮事件
    document.getElementById('task-add-queue').onclick = function() {
      var name = document.getElementById('task-name').value.trim();
      var script = document.getElementById('script-name').value.trim();
      var staging_template_dir = document.getElementById('script-path-on').value.trim();
      if (!name) {
        alert('请填写完整信息');
        return false;
      }
      // 构建workflow参数，根据您提供的接口规范
      const workflowData = {
        workflow: {
          name: name ? name : "默认任务",
          script_name: script,
          staging_template_dir: staging_template_dir
        },
        commit: "创建新作业"
      };

      // 调用加入队列API
      fetch('https://*************/pun/sys/myjobs/create_from_path.json', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(workflowData)
      })
      .then(res => res.json())
      .then(data => {
        if (data && (data.success === true)) {
          var workflow_id = data.workflow.id;
          const url = `/pun/sys/myjobs/workflows/${workflow_id}/submit.json`;
          console.log("第二次请求的 URL: " + url);
          fetch(url, {
            method: 'PUT',
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(workflowData)
          })
          // .then(res => res.json())
          .then(data => {
            console.log(data);
            if (data && (data.status === 200)) {
              alert('已成功加入队列');
              document.getElementById('view-template-modal').style.display = 'none';
            } else {
              alert(data.message || '加入队列失败，请检查脚本文件');
            }
          })
          .catch(err => {
            alert('请求失败: ' + err);
          });
        } else {
          alert(data.message || '加入队列失败2');
        }
      })
      .catch(err => {
        alert('请求失败: ' + err);
      });
    };

    function redirectToFiles() {
      window.location.href = "<%= files_path(Dir.home) %>";
    }
  </script>
</div>